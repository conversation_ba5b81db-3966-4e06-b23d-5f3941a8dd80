package com.yt.subject.domain.convert;

import com.yt.subject.domain.entity.SubjectCategoryBo;
import com.yt.subject.infra.basic.entity.SubjectCategory;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T00:24:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class CategoryBoConvertImpl implements CategoryBoConvert {

    @Override
    public SubjectCategory boToY(SubjectCategoryBo subjectCategory) {
        if ( subjectCategory == null ) {
            return null;
        }

        SubjectCategory subjectCategory1 = new SubjectCategory();

        subjectCategory1.setId( subjectCategory.getId() );
        subjectCategory1.setCategoryName( subjectCategory.getCategoryName() );
        subjectCategory1.setCategoryType( subjectCategory.getCategoryType() );
        subjectCategory1.setImageUrl( subjectCategory.getImageUrl() );
        subjectCategory1.setParentId( subjectCategory.getParentId() );

        return subjectCategory1;
    }

    @Override
    public List<SubjectCategoryBo> yToBo(List<SubjectCategory> subjectCategory) {
        if ( subjectCategory == null ) {
            return null;
        }

        List<SubjectCategoryBo> list = new ArrayList<SubjectCategoryBo>( subjectCategory.size() );
        for ( SubjectCategory subjectCategory1 : subjectCategory ) {
            list.add( subjectCategoryToSubjectCategoryBo( subjectCategory1 ) );
        }

        return list;
    }

    @Override
    public List<SubjectCategory> boToyList(List<SubjectCategoryBo> subjectCategory) {
        if ( subjectCategory == null ) {
            return null;
        }

        List<SubjectCategory> list = new ArrayList<SubjectCategory>( subjectCategory.size() );
        for ( SubjectCategoryBo subjectCategoryBo : subjectCategory ) {
            list.add( boToY( subjectCategoryBo ) );
        }

        return list;
    }

    protected SubjectCategoryBo subjectCategoryToSubjectCategoryBo(SubjectCategory subjectCategory) {
        if ( subjectCategory == null ) {
            return null;
        }

        SubjectCategoryBo subjectCategoryBo = new SubjectCategoryBo();

        subjectCategoryBo.setId( subjectCategory.getId() );
        subjectCategoryBo.setCategoryName( subjectCategory.getCategoryName() );
        subjectCategoryBo.setCategoryType( subjectCategory.getCategoryType() );
        subjectCategoryBo.setImageUrl( subjectCategory.getImageUrl() );
        subjectCategoryBo.setParentId( subjectCategory.getParentId() );

        return subjectCategoryBo;
    }
}
