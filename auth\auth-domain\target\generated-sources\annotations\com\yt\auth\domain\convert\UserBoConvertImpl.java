package com.yt.auth.domain.convert;

import com.yt.auth.domain.entity.AuthUserBo;
import com.yt.auth.infra.basic.entity.AuthUser;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:33:31+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class UserBoConvertImpl implements UserBoConvert {

    @Override
    public AuthUser boToy(AuthUserBo authRolePermissionBo) {
        if ( authRolePermissionBo == null ) {
            return null;
        }

        AuthUser authUser = new AuthUser();

        authUser.setId( authRolePermissionBo.getId() );
        authUser.setUserName( authRolePermissionBo.getUserName() );
        authUser.setNickName( authRolePermissionBo.getNickName() );
        authUser.setEmail( authRolePermissionBo.getEmail() );
        authUser.setPhone( authRolePermissionBo.getPhone() );
        authUser.setPassword( authRolePermissionBo.getPassword() );
        authUser.setSex( authRolePermissionBo.getSex() );
        authUser.setAvatar( authRolePermissionBo.getAvatar() );
        authUser.setStatus( authRolePermissionBo.getStatus() );
        authUser.setIntroduce( authRolePermissionBo.getIntroduce() );
        authUser.setExtJson( authRolePermissionBo.getExtJson() );
        authUser.setIsDeleted( authRolePermissionBo.getIsDeleted() );

        return authUser;
    }

    @Override
    public AuthUserBo yToBo(AuthUser authUser) {
        if ( authUser == null ) {
            return null;
        }

        AuthUserBo authUserBo = new AuthUserBo();

        authUserBo.setId( authUser.getId() );
        authUserBo.setUserName( authUser.getUserName() );
        authUserBo.setNickName( authUser.getNickName() );
        authUserBo.setEmail( authUser.getEmail() );
        authUserBo.setPhone( authUser.getPhone() );
        authUserBo.setPassword( authUser.getPassword() );
        authUserBo.setSex( authUser.getSex() );
        authUserBo.setAvatar( authUser.getAvatar() );
        authUserBo.setStatus( authUser.getStatus() );
        authUserBo.setIntroduce( authUser.getIntroduce() );
        authUserBo.setExtJson( authUser.getExtJson() );
        authUserBo.setIsDeleted( authUser.getIsDeleted() );

        return authUserBo;
    }
}
