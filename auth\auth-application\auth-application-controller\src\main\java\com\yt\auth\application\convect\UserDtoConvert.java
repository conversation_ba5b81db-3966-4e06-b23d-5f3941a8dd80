package com.yt.auth.application.convect;



import com.yt.auth.domain.entity.AuthUserBo;
import com.yt.subject.api.entity.AuthUserDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface UserDtoConvert {
    UserDtoConvert INSTANCE = Mappers.getMapper(UserDtoConvert.class);
    AuthUserBo dtoToBo(AuthUserDto authUserDto);
    AuthUserDto boToDto(AuthUserBo authUserBo);
}
