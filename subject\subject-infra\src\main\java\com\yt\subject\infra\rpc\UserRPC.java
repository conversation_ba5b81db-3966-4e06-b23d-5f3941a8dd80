package com.yt.subject.infra.rpc;

import com.yt.subject.api.entity.AuthUserDto;
import com.yt.subject.api.entity.Result;
import com.yt.subject.api.feignService.UserFeignService;

import com.yt.subject.infra.entity.UserInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class UserRPC {
@Resource
    private UserFeignService userFeignService;
public UserInfo getUserInfo(String userName) {
    AuthUserDto authUserDto =new AuthUserDto();
    authUserDto.setUserName(userName);
    Result<AuthUserDto> result = userFeignService.getUserInfo(authUserDto);
    UserInfo userInfo = new UserInfo();
    if (!result.getSuccess()) {
        return userInfo;
    }

        AuthUserDto data = result.getData();
        userInfo.setUserName(data.getUserName());
        userInfo.setNickName(data.getNickName());
    return userInfo;
}
}
