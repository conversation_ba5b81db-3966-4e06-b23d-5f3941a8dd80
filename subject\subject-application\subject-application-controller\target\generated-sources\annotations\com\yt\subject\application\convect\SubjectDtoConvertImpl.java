package com.yt.subject.application.convect;

import com.yt.subject.application.entity.SubjectAnswerDto;
import com.yt.subject.application.entity.SubjectInfoDto;
import com.yt.subject.domain.entity.SubjectAnswerBo;
import com.yt.subject.domain.entity.SubjectInfoBo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T00:24:15+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class SubjectDtoConvertImpl implements SubjectDtoConvert {

    @Override
    public SubjectInfoBo dtoToBo(SubjectInfoDto subjectDto) {
        if ( subjectDto == null ) {
            return null;
        }

        SubjectInfoBo subjectInfoBo = new SubjectInfoBo();

        subjectInfoBo.setPageNo( subjectDto.getPageNo() );
        subjectInfoBo.setPageSize( subjectDto.getPageSize() );
        subjectInfoBo.setId( subjectDto.getId() );
        subjectInfoBo.setSubjectName( subjectDto.getSubjectName() );
        subjectInfoBo.setSubjectDifficult( subjectDto.getSubjectDifficult() );
        subjectInfoBo.setSettleName( subjectDto.getSettleName() );
        subjectInfoBo.setSubjectType( subjectDto.getSubjectType() );
        subjectInfoBo.setSubjectScore( subjectDto.getSubjectScore() );
        subjectInfoBo.setSubjectParse( subjectDto.getSubjectParse() );
        subjectInfoBo.setSubjectAnswer( subjectDto.getSubjectAnswer() );
        List<Integer> list = subjectDto.getCategoryIds();
        if ( list != null ) {
            subjectInfoBo.setCategoryIds( new ArrayList<Integer>( list ) );
        }
        List<Integer> list1 = subjectDto.getLabelIds();
        if ( list1 != null ) {
            subjectInfoBo.setLabelIds( new ArrayList<Integer>( list1 ) );
        }
        subjectInfoBo.setOptionList( subjectAnswerDtoListToSubjectAnswerBoList( subjectDto.getOptionList() ) );
        if ( subjectDto.getCategoryId() != null ) {
            subjectInfoBo.setCategoryId( subjectDto.getCategoryId().longValue() );
        }
        if ( subjectDto.getLabelId() != null ) {
            subjectInfoBo.setLabelId( subjectDto.getLabelId().longValue() );
        }

        return subjectInfoBo;
    }

    @Override
    public List<SubjectInfoDto> boToDto(List<SubjectInfoBo> subjectInfoBoList) {
        if ( subjectInfoBoList == null ) {
            return null;
        }

        List<SubjectInfoDto> list = new ArrayList<SubjectInfoDto>( subjectInfoBoList.size() );
        for ( SubjectInfoBo subjectInfoBo : subjectInfoBoList ) {
            list.add( subjectInfoBoToSubjectInfoDto( subjectInfoBo ) );
        }

        return list;
    }

    protected SubjectAnswerBo subjectAnswerDtoToSubjectAnswerBo(SubjectAnswerDto subjectAnswerDto) {
        if ( subjectAnswerDto == null ) {
            return null;
        }

        SubjectAnswerBo subjectAnswerBo = new SubjectAnswerBo();

        subjectAnswerBo.setOptionType( subjectAnswerDto.getOptionType() );
        subjectAnswerBo.setOptionContent( subjectAnswerDto.getOptionContent() );
        subjectAnswerBo.setIsCorrect( subjectAnswerDto.getIsCorrect() );

        return subjectAnswerBo;
    }

    protected List<SubjectAnswerBo> subjectAnswerDtoListToSubjectAnswerBoList(List<SubjectAnswerDto> list) {
        if ( list == null ) {
            return null;
        }

        List<SubjectAnswerBo> list1 = new ArrayList<SubjectAnswerBo>( list.size() );
        for ( SubjectAnswerDto subjectAnswerDto : list ) {
            list1.add( subjectAnswerDtoToSubjectAnswerBo( subjectAnswerDto ) );
        }

        return list1;
    }

    protected SubjectAnswerDto subjectAnswerBoToSubjectAnswerDto(SubjectAnswerBo subjectAnswerBo) {
        if ( subjectAnswerBo == null ) {
            return null;
        }

        SubjectAnswerDto subjectAnswerDto = new SubjectAnswerDto();

        subjectAnswerDto.setOptionType( subjectAnswerBo.getOptionType() );
        subjectAnswerDto.setOptionContent( subjectAnswerBo.getOptionContent() );
        subjectAnswerDto.setIsCorrect( subjectAnswerBo.getIsCorrect() );

        return subjectAnswerDto;
    }

    protected List<SubjectAnswerDto> subjectAnswerBoListToSubjectAnswerDtoList(List<SubjectAnswerBo> list) {
        if ( list == null ) {
            return null;
        }

        List<SubjectAnswerDto> list1 = new ArrayList<SubjectAnswerDto>( list.size() );
        for ( SubjectAnswerBo subjectAnswerBo : list ) {
            list1.add( subjectAnswerBoToSubjectAnswerDto( subjectAnswerBo ) );
        }

        return list1;
    }

    protected SubjectInfoDto subjectInfoBoToSubjectInfoDto(SubjectInfoBo subjectInfoBo) {
        if ( subjectInfoBo == null ) {
            return null;
        }

        SubjectInfoDto subjectInfoDto = new SubjectInfoDto();

        subjectInfoDto.setPageNo( subjectInfoBo.getPageNo() );
        subjectInfoDto.setPageSize( subjectInfoBo.getPageSize() );
        subjectInfoDto.setId( subjectInfoBo.getId() );
        subjectInfoDto.setSubjectName( subjectInfoBo.getSubjectName() );
        subjectInfoDto.setSubjectDifficult( subjectInfoBo.getSubjectDifficult() );
        subjectInfoDto.setSettleName( subjectInfoBo.getSettleName() );
        subjectInfoDto.setSubjectType( subjectInfoBo.getSubjectType() );
        subjectInfoDto.setSubjectScore( subjectInfoBo.getSubjectScore() );
        subjectInfoDto.setSubjectParse( subjectInfoBo.getSubjectParse() );
        subjectInfoDto.setSubjectAnswer( subjectInfoBo.getSubjectAnswer() );
        List<Integer> list = subjectInfoBo.getCategoryIds();
        if ( list != null ) {
            subjectInfoDto.setCategoryIds( new ArrayList<Integer>( list ) );
        }
        List<Integer> list1 = subjectInfoBo.getLabelIds();
        if ( list1 != null ) {
            subjectInfoDto.setLabelIds( new ArrayList<Integer>( list1 ) );
        }
        subjectInfoDto.setOptionList( subjectAnswerBoListToSubjectAnswerDtoList( subjectInfoBo.getOptionList() ) );
        if ( subjectInfoBo.getCategoryId() != null ) {
            subjectInfoDto.setCategoryId( subjectInfoBo.getCategoryId().intValue() );
        }
        if ( subjectInfoBo.getLabelId() != null ) {
            subjectInfoDto.setLabelId( subjectInfoBo.getLabelId().intValue() );
        }

        return subjectInfoDto;
    }
}
