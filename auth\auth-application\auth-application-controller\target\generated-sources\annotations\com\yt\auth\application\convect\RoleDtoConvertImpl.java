package com.yt.auth.application.convect;

import com.yt.auth.application.entity.AuthRoleDto;
import com.yt.auth.domain.entity.AuthRoleBo;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:08:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class RoleDtoConvertImpl implements RoleDtoConvert {

    @Override
    public AuthRoleBo dtoToBo(AuthRoleDto authRoleDto) {
        if ( authRoleDto == null ) {
            return null;
        }

        AuthRoleBo authRoleBo = new AuthRoleBo();

        authRoleBo.setId( authRoleDto.getId() );
        authRoleBo.setRoleName( authRoleDto.getRoleName() );
        authRoleBo.setRoleKey( authRoleDto.getRoleKey() );
        authRoleBo.setIsDeleted( authRoleDto.getIsDeleted() );

        return authRoleBo;
    }
}
