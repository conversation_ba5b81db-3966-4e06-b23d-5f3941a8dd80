package com.yt.auth.domain.convert;

import com.yt.auth.domain.entity.AuthRoleBo;
import com.yt.auth.infra.basic.entity.AuthRole;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:33:31+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class RoleBoConvertImpl implements RoleBoConvert {

    @Override
    public AuthRole boToy(AuthRoleBo authRoleBo) {
        if ( authRoleBo == null ) {
            return null;
        }

        AuthRole authRole = new AuthRole();

        authRole.setId( authRoleBo.getId() );
        authRole.setRoleName( authRoleBo.getRoleName() );
        authRole.setRoleKey( authRoleBo.getRoleKey() );
        authRole.setIsDeleted( authRoleBo.getIsDeleted() );

        return authRole;
    }
}
