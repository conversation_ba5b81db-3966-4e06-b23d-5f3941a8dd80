package com.yt.gateway.filter;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class loginFilter implements GlobalFilter {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String url = request.getURI().getPath();
        log.info("LoginFilter.filter.url:{}", url);
        if ("/auth/user/doLogin".equals(url)) {
            return chain.filter(exchange);
        }
        try {
            SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
            log.info("LoginFilter.filter.tokenInfo:{}", new Gson().toJson(tokenInfo));
            String loginId = (String) tokenInfo.getLoginId();
            ServerHttpRequest newRequest = request.mutate().header("loginId", loginId).build();
            return chain.filter(exchange.mutate().request(newRequest).build());
        } catch (Exception e) {
            log.warn("Not logged in, proceeding without loginId header for url: {}", url);
            return chain.filter(exchange);
        }
    }
}