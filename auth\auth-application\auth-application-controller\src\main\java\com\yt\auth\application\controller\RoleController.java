package com.yt.auth.application.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.yt.auth.application.convect.RoleDtoConvert;
import com.yt.auth.application.entity.AuthRoleDto;

import com.yt.auth.domain.entity.AuthRoleBo;
import com.yt.auth.domain.service.AuthRoleDomainService;
import com.yt.subject.api.entity.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/role")
@Slf4j
public class RoleController {

    @Resource
    private AuthRoleDomainService authRoleDomainService;

    /**
     *  新增角色
     */
    @RequestMapping("/add")
    public Result<Boolean> add(@RequestBody AuthRoleDto authRoleDto){
        try {
            if(log.isInfoEnabled()) {
                log.info("UserController.add.controller:{}", JSON.toJSONString(authRoleDto));
            }
            Preconditions.checkArgument(!StringUtils.isBlank(authRoleDto.getRoleKey()),"角色key不能为空");
            Preconditions.checkArgument(!StringUtils.isBlank(authRoleDto.getRoleName()),"角色名称不能为空");
            AuthRoleBo authRoleBo = RoleDtoConvert.INSTANCE.dtoToBo(authRoleDto);
            return Result.success(authRoleDomainService.add(authRoleBo));
        }catch (Exception e){
            log.error("UserController.add error:{}", e.getMessage(),e);
            return Result.error("更新角色状态失败");
        }
    }

    /**
     *  更新角色
     */
    @RequestMapping("/update")
    public Result<Boolean> update(@RequestBody AuthRoleDto authRoleDto){
        try {
            if(log.isInfoEnabled()) {
                log.info("UserController.update.controller:{}", JSON.toJSONString(authRoleDto));
            }
            Preconditions.checkNotNull(authRoleDto.getId(),"角色Id不能为空");
            Preconditions.checkArgument(!StringUtils.isBlank(authRoleDto.getRoleName()),"角色名称不能为空");
            AuthRoleBo authRoleBo = RoleDtoConvert.INSTANCE.dtoToBo(authRoleDto);
            return Result.success(authRoleDomainService.update(authRoleBo));
        }catch (Exception e){
            log.error("UserController.update error:{}", e.getMessage(),e);
            return Result.error("更新角色失败");
        }
    }

    /**
     *  删除角色
     */
    @RequestMapping("/delete")
    public Result<Boolean> delete(@RequestBody AuthRoleDto authRoleDto){
        try {
            if(log.isInfoEnabled()) {
                log.info("UserController.delete.controller:{}", JSON.toJSONString(authRoleDto));
            }
            Preconditions.checkNotNull(authRoleDto.getId(),"角色Id不能为空");
            AuthRoleBo authRoleBo = RoleDtoConvert.INSTANCE.dtoToBo(authRoleDto);
            return Result.success(authRoleDomainService.delete(authRoleBo));
        }catch (Exception e){
            log.error("UserController.delete error:{}", e.getMessage(),e);
            return Result.error("删除角色失败");
        }
    }
}
