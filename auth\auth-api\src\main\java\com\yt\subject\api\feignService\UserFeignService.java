package com.yt.subject.api.feignService;

import com.yt.subject.api.entity.AuthUserDto;
import com.yt.subject.api.entity.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient("auth")
public interface UserFeignService {
    @RequestMapping("/user/getUserInfo")
    public Result<AuthUserDto> getUserInfo(@RequestBody AuthUserDto authUserDto);
}
