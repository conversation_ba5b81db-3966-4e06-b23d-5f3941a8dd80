<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="08e93c48-eb69-4e75-b7f3-976d4a8059ad" name="更改" comment="二期多线程提交" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="application.yml" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PUSH_AUTO_UPDATE" value="true" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/loginFilter.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/gateway/src/main/resources/bootstrap.yaml" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Maven" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\Maven\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zyxcUlIyO9BmMFi7KQ3KMzAsov" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="sortKey" value="BY_TIME_ASCENDING" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "HTTP 请求.generated-requests | #6.executor": "Run",
    "Maven.auth [clean].executor": "Run",
    "Maven.auth [install].executor": "Run",
    "Maven.auth-api [clean].executor": "Run",
    "Maven.gateway [clean].executor": "Run",
    "Maven.gateway [install].executor": "Run",
    "Maven.oss [clean].executor": "Run",
    "Maven.subject [clean].executor": "Run",
    "Maven.wx [clean].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.AuthApplication.executor": "Run",
    "Spring Boot.GatewayApplication.executor": "Run",
    "Spring Boot.OSSApplication.executor": "Run",
    "Spring Boot.SubjectApplication.executor": "Run",
    "Spring Boot.WxApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "B:/ideaBook/nwct",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "onboarding.tips.debug.path": "B:/ideaBook/club/wx/src/main/java/com/yt/Main.java",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.37356323",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="com.yt.auth.application.controller" />
      <recent name="com.yt.auth.domain.service" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="B:\ideaBook\club\subject\subject-application\subject-application-controller\src\main\java\com\yt\subject\application" />
      <recent name="B:\ideaBook\club\subject\subject-commom\src\main\java\com\yt\subject\commom" />
      <recent name="B:\ideaBook\club\subject\subject-application\subject-application-controller\src\main\java\com\yt\subject\application\Interceptor" />
      <recent name="B:\ideaBook\club\gateway\src\main\java\com\yt\gateway\entity" />
      <recent name="B:\ideaBook\club\gateway\src\main\java\com\yt\gateway" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.yt.subject.application.controller" />
      <recent name="com.yt.wx.config" />
      <recent name="com.yt.wx.utils" />
      <recent name="com.yt.auth.domain.convert" />
      <recent name="com.yt.auth.domain.entity" />
    </key>
  </component>
  <component name="RedisHelper">
    <option name="connections">
      <list>
        <ConnectionInfo>
          <option name="global" value="false" />
          <option name="id" value="4b167219-506b-46f1-80e3-33d1deadc7b7" />
          <option name="name" value="club" />
          <option name="port" value="6379" />
          <option name="url" value="117.72.127.194" />
        </ConnectionInfo>
      </list>
    </option>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
    <option name="excludedTypes">
      <set>
        <option value="HttpClient.HttpRequestRunConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.GatewayApplication">
    <configuration name="generated-requests | #6" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" index="6" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="AuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="auth-starter" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yt.auth.AuthApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yt.auth.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yt.gateway.GatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OSSApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="oss" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yt.oss.OSSApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SubjectApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="subject-starter" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yt.subject.SubjectApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WxApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="wx" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yt.wx.WxApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yt.wx.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="HTTP 请求.generated-requests | #6" />
      <item itemvalue="Spring Boot.SubjectApplication" />
      <item itemvalue="Spring Boot.OSSApplication" />
      <item itemvalue="Spring Boot.GatewayApplication" />
      <item itemvalue="Spring Boot.AuthApplication" />
      <item itemvalue="Spring Boot.WxApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.AuthApplication" />
        <item itemvalue="Spring Boot.WxApplication" />
        <item itemvalue="HTTP 请求.generated-requests | #6" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.19416.15" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.19416.15" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="08e93c48-eb69-4e75-b7f3-976d4a8059ad" name="更改" comment="" />
      <created>1752717195965</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752717195965</updated>
      <workItem from="1752717198249" duration="23697000" />
      <workItem from="1752807785319" duration="17926000" />
      <workItem from="1753060167834" duration="52135000" />
      <workItem from="1753234323821" duration="27487000" />
      <workItem from="1753338670750" duration="4980000" />
      <workItem from="1753356092131" duration="1867000" />
      <workItem from="1753371926160" duration="32000" />
      <workItem from="1753625123204" duration="6718000" />
      <workItem from="1753662476635" duration="9669000" />
      <workItem from="1753783673354" duration="9186000" />
      <workItem from="1753857541140" duration="10831000" />
      <workItem from="1754149749241" duration="1080000" />
      <workItem from="1754197171766" duration="9458000" />
      <workItem from="1754229376280" duration="4195000" />
      <workItem from="1754235937021" duration="519000" />
      <workItem from="1754237563976" duration="16509000" />
    </task>
    <task id="LOCAL-00001" summary="骨架完成">
      <option name="closed" value="true" />
      <created>1752717374745</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752717374745</updated>
    </task>
    <task id="LOCAL-00002" summary="基础信息完成">
      <option name="closed" value="true" />
      <created>1752759405621</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752759405621</updated>
    </task>
    <task id="LOCAL-00003" summary="缓存">
      <option name="closed" value="true" />
      <created>1752806820940</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752806820940</updated>
    </task>
    <task id="LOCAL-00004" summary="所有基础代码完成">
      <option name="closed" value="true" />
      <created>1752812793046</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752812793046</updated>
    </task>
    <task id="LOCAL-00005" summary="wx模块">
      <option name="closed" value="true" />
      <created>1753076021796</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753076021796</updated>
    </task>
    <task id="LOCAL-00006" summary="一期规划完成">
      <option name="closed" value="true" />
      <created>1753240338257</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753240338257</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="骨架完成" />
    <MESSAGE value="基础信息完成" />
    <MESSAGE value="缓存" />
    <MESSAGE value="所有基础代码完成" />
    <MESSAGE value="wx模块" />
    <MESSAGE value="一期规划完成" />
    <MESSAGE value="二期多线程提交" />
    <option name="LAST_COMMIT_MESSAGE" value="二期多线程提交" />
    <option name="GROUP_MULTIFILE_MERGE_BY_DIRECTORY" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/auth/auth-application/auth-application-controller/src/main/java/com/yt/auth/application/controller/UserController.java</url>
          <line>93</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.yt.subject.domain.entity.SubjectCategoryBo" memberName="labelBOList" />
        <PinnedItemInfo parentTag="java.lang.Long" memberName="value" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="dtoList" language="JAVA" />
        <watch expression="exchange" language="JAVA" />
        <watch expression="exchange" />
        <watch expression="exchange" />
        <watch expression="exchange" language="JAVA" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>