package com.yt.subject.domain.convert;

import com.yt.subject.domain.entity.SubjectAnswerBo;
import com.yt.subject.infra.basic.entity.SubjectRadio;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T00:24:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class RadioConverterImpl implements RadioConverter {

    @Override
    public SubjectRadio convertBoToEntity(SubjectAnswerBo subjectAnswerBO) {
        if ( subjectAnswerBO == null ) {
            return null;
        }

        SubjectRadio subjectRadio = new SubjectRadio();

        if ( subjectAnswerBO.getOptionType() != null ) {
            subjectRadio.setOptionType( subjectAnswerBO.getOptionType().intValue() );
        }
        subjectRadio.setOptionContent( subjectAnswerBO.getOptionContent() );
        subjectRadio.setIsCorrect( subjectAnswerBO.getIsCorrect() );

        return subjectRadio;
    }

    @Override
    public List<SubjectAnswerBo> convertEntityToBoList(List<SubjectRadio> subjectRadioList) {
        if ( subjectRadioList == null ) {
            return null;
        }

        List<SubjectAnswerBo> list = new ArrayList<SubjectAnswerBo>( subjectRadioList.size() );
        for ( SubjectRadio subjectRadio : subjectRadioList ) {
            list.add( subjectRadioToSubjectAnswerBo( subjectRadio ) );
        }

        return list;
    }

    protected SubjectAnswerBo subjectRadioToSubjectAnswerBo(SubjectRadio subjectRadio) {
        if ( subjectRadio == null ) {
            return null;
        }

        SubjectAnswerBo subjectAnswerBo = new SubjectAnswerBo();

        if ( subjectRadio.getOptionType() != null ) {
            subjectAnswerBo.setOptionType( subjectRadio.getOptionType().longValue() );
        }
        subjectAnswerBo.setOptionContent( subjectRadio.getOptionContent() );
        subjectAnswerBo.setIsCorrect( subjectRadio.getIsCorrect() );

        return subjectAnswerBo;
    }
}
