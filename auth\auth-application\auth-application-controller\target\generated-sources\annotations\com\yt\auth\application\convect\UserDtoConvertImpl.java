package com.yt.auth.application.convect;

import com.yt.auth.domain.entity.AuthUserBo;
import com.yt.subject.api.entity.AuthUserDto;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:08:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class UserDtoConvertImpl implements UserDtoConvert {

    @Override
    public AuthUserBo dtoToBo(AuthUserDto authUserDto) {
        if ( authUserDto == null ) {
            return null;
        }

        AuthUserBo authUserBo = new AuthUserBo();

        authUserBo.setId( authUserDto.getId() );
        authUserBo.setUserName( authUserDto.getUserName() );
        authUserBo.setNickName( authUserDto.getNickName() );
        authUserBo.setEmail( authUserDto.getEmail() );
        authUserBo.setPhone( authUserDto.getPhone() );
        authUserBo.setPassword( authUserDto.getPassword() );
        authUserBo.setSex( authUserDto.getSex() );
        authUserBo.setAvatar( authUserDto.getAvatar() );
        authUserBo.setStatus( authUserDto.getStatus() );
        authUserBo.setIntroduce( authUserDto.getIntroduce() );
        authUserBo.setExtJson( authUserDto.getExtJson() );
        authUserBo.setIsDeleted( authUserDto.getIsDeleted() );

        return authUserBo;
    }

    @Override
    public AuthUserDto boToDto(AuthUserBo authUserBo) {
        if ( authUserBo == null ) {
            return null;
        }

        AuthUserDto authUserDto = new AuthUserDto();

        authUserDto.setId( authUserBo.getId() );
        authUserDto.setUserName( authUserBo.getUserName() );
        authUserDto.setNickName( authUserBo.getNickName() );
        authUserDto.setEmail( authUserBo.getEmail() );
        authUserDto.setPhone( authUserBo.getPhone() );
        authUserDto.setPassword( authUserBo.getPassword() );
        authUserDto.setSex( authUserBo.getSex() );
        authUserDto.setAvatar( authUserBo.getAvatar() );
        authUserDto.setStatus( authUserBo.getStatus() );
        authUserDto.setIntroduce( authUserBo.getIntroduce() );
        authUserDto.setExtJson( authUserBo.getExtJson() );
        authUserDto.setIsDeleted( authUserBo.getIsDeleted() );

        return authUserDto;
    }
}
