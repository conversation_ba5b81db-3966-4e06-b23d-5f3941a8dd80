package com.yt.oss.controller;


import com.yt.oss.service.FileService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@Configuration
@RefreshScope
public class FileController {

    @Resource
    private FileService service;

    @PostMapping("/upload")
    public String upFile(MultipartFile uploadFile, String bucket, String objectName) {
        String s = service.uploadFile(uploadFile, bucket, objectName);
        System.out.println(s);
        return s;
    }

    @GetMapping("/testGetAllBuckets")
    public String testGetAllBuckets() {
        List<String> allBucket = service.listBuckets();
        return allBucket.get(0);
    }

    @GetMapping("/getUrl")
    public String getUrl(@RequestParam String bucketName, @RequestParam String objectName) {
        return service.getPresignedObjectUrl(bucketName, objectName);
    }

    @GetMapping("/download")
    public ResponseEntity<byte[]> download(@RequestParam String bucket,
                                           @RequestParam String objectName) {
        byte[] fileBytes = service.downloadFileAsBytes(bucket, objectName);
        if (fileBytes == null || fileBytes.length == 0) {
            return ResponseEntity.notFound().build();
        }
        String fileName = objectName.contains("/") ? objectName.substring(objectName.lastIndexOf("/") + 1) : objectName;
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.setContentDispositionFormData("attachment", URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        } catch (Exception e) {
            headers.setContentDispositionFormData("attachment", "file_download");
        }
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return ResponseEntity.ok()
                .headers(headers)
                .body(fileBytes);
    }
}