package com.yt.subject.domain.convert;

import com.yt.subject.domain.entity.SubjectInfoBo;
import com.yt.subject.infra.basic.entity.SubjectBrief;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T00:24:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class BriefConverterImpl implements BriefConverter {

    @Override
    public SubjectBrief convertBoToEntity(SubjectInfoBo subjectInfoBO) {
        if ( subjectInfoBO == null ) {
            return null;
        }

        SubjectBrief subjectBrief = new SubjectBrief();

        subjectBrief.setId( subjectInfoBO.getId() );
        subjectBrief.setSubjectAnswer( subjectInfoBO.getSubjectAnswer() );

        return subjectBrief;
    }
}
