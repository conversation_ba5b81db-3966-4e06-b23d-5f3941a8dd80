package com.yt.subject;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;


/**
 * <AUTHOR>
 */
@SpringBootApplication
@ComponentScan("com.yt")
@MapperScan("com.yt.**.dao")
@EnableFeignClients(basePackages = "com.yt")
public class SubjectApplication {
    public static void main(String[] args) {
        SpringApplication.run(SubjectApplication.class, args);
    }
}
