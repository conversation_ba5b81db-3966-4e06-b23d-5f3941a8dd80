package com.yt.subject.application.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.yt.subject.application.convect.CategoryDtoConvert;
import com.yt.subject.application.convect.LabelDtoConvert;
import com.yt.subject.application.entity.SubjectCategoryDto;
import com.yt.subject.application.entity.SubjectLabelDto;
import com.yt.subject.commom.entity.Result;
import com.yt.subject.domain.entity.SubjectCategoryBo;
import com.yt.subject.domain.service.SubjectCategoryDomainService;
import com.yt.subject.infra.entity.UserInfo;
import com.yt.subject.infra.rpc.UserRPC;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 刷题分类Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/subject/category")
@Slf4j
public class testFeignController {
    @Resource
    private UserRPC userRPC;
@GetMapping("testFeign")
    public void testFeign() {
        UserInfo userInfo = userRPC.getUserInfo("qtz");
        log.info("testFeign.userId:{}",userInfo);
    }

}