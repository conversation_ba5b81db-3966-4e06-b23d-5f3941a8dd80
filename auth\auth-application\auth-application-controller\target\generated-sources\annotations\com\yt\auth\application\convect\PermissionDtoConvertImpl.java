package com.yt.auth.application.convect;

import com.yt.auth.application.entity.AuthPermissionDto;
import com.yt.auth.domain.entity.AuthPermissionBo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T17:08:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class PermissionDtoConvertImpl implements PermissionDtoConvert {

    @Override
    public AuthPermissionBo dtoToBo(AuthPermissionDto authPermissionDto) {
        if ( authPermissionDto == null ) {
            return null;
        }

        AuthPermissionBo authPermissionBo = new AuthPermissionBo();

        authPermissionBo.setId( authPermissionDto.getId() );
        authPermissionBo.setName( authPermissionDto.getName() );
        authPermissionBo.setParentId( authPermissionDto.getParentId() );
        authPermissionBo.setType( authPermissionDto.getType() );
        authPermissionBo.setMenuUrl( authPermissionDto.getMenuUrl() );
        authPermissionBo.setStatus( authPermissionDto.getStatus() );
        authPermissionBo.setShow( authPermissionDto.getShow() );
        authPermissionBo.setIcon( authPermissionDto.getIcon() );
        authPermissionBo.setPermissionKey( authPermissionDto.getPermissionKey() );
        authPermissionBo.setIsDeleted( authPermissionDto.getIsDeleted() );

        return authPermissionBo;
    }

    @Override
    public List<AuthPermissionDto> boToDto(List<AuthPermissionBo> authPermissionBo) {
        if ( authPermissionBo == null ) {
            return null;
        }

        List<AuthPermissionDto> list = new ArrayList<AuthPermissionDto>( authPermissionBo.size() );
        for ( AuthPermissionBo authPermissionBo1 : authPermissionBo ) {
            list.add( authPermissionBoToAuthPermissionDto( authPermissionBo1 ) );
        }

        return list;
    }

    protected AuthPermissionDto authPermissionBoToAuthPermissionDto(AuthPermissionBo authPermissionBo) {
        if ( authPermissionBo == null ) {
            return null;
        }

        AuthPermissionDto authPermissionDto = new AuthPermissionDto();

        authPermissionDto.setId( authPermissionBo.getId() );
        authPermissionDto.setName( authPermissionBo.getName() );
        authPermissionDto.setParentId( authPermissionBo.getParentId() );
        authPermissionDto.setType( authPermissionBo.getType() );
        authPermissionDto.setMenuUrl( authPermissionBo.getMenuUrl() );
        authPermissionDto.setStatus( authPermissionBo.getStatus() );
        authPermissionDto.setShow( authPermissionBo.getShow() );
        authPermissionDto.setIcon( authPermissionBo.getIcon() );
        authPermissionDto.setPermissionKey( authPermissionBo.getPermissionKey() );
        authPermissionDto.setIsDeleted( authPermissionBo.getIsDeleted() );

        return authPermissionDto;
    }
}
