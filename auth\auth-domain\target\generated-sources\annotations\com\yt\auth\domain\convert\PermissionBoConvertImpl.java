package com.yt.auth.domain.convert;

import com.yt.auth.domain.entity.AuthPermissionBo;
import com.yt.auth.infra.basic.entity.AuthPermission;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:33:31+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class PermissionBoConvertImpl implements PermissionBoConvert {

    @Override
    public AuthPermission boToy(AuthPermissionBo permissionBo) {
        if ( permissionBo == null ) {
            return null;
        }

        AuthPermission authPermission = new AuthPermission();

        authPermission.setId( permissionBo.getId() );
        authPermission.setName( permissionBo.getName() );
        authPermission.setParentId( permissionBo.getParentId() );
        authPermission.setType( permissionBo.getType() );
        authPermission.setMenuUrl( permissionBo.getMenuUrl() );
        authPermission.setStatus( permissionBo.getStatus() );
        authPermission.setShow( permissionBo.getShow() );
        authPermission.setIcon( permissionBo.getIcon() );
        authPermission.setPermissionKey( permissionBo.getPermissionKey() );
        authPermission.setIsDeleted( permissionBo.getIsDeleted() );

        return authPermission;
    }
}
