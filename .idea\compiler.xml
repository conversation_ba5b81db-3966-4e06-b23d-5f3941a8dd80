<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="oss" />
        <module name="gateway" />
      </profile>
      <profile name="Annotation profile for wx" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
        </processorPath>
        <module name="wx" />
        <module name="subject-infra" />
        <module name="subject-commom" />
        <module name="subject-starter" />
      </profile>
      <profile name="Annotation profile for auth" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <option name="mapstruct.unmappedTargetPolicy" value="IGNORE" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="auth-domain" />
        <module name="auth-api" />
        <module name="auth-infra" />
        <module name="auth-starter" />
        <module name="auth-common" />
        <module name="auth-application-controller" />
      </profile>
      <profile name="Annotation profile for subject-application-controller" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="subject-application-controller" />
      </profile>
      <profile name="Annotation profile for subject-domain" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="subject-domain" />
      </profile>
      <profile name="Annotation profile for subject" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct-processor/1.5.5.Final/mapstruct-processor-1.5.5.Final.jar" />
          <entry name="$PROJECT_DIR$/../../repository/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar" />
        </processorPath>
        <module name="subject-api" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="auth-api" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
      <module name="auth-application-controller" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
      <module name="auth-common" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
      <module name="auth-domain" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
      <module name="auth-infra" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
      <module name="auth-starter" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
      <module name="subject-api" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
      <module name="subject-application-controller" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
      <module name="subject-commom" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
      <module name="subject-domain" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
      <module name="subject-infra" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
      <module name="subject-starter" options="-Amapstruct.unmappedTargetPolicy=IGNORE" />
    </option>
  </component>
</project>