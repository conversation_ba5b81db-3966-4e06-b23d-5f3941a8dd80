package com.yt.auth.application.controller;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.yt.auth.application.convect.UserDtoConvert;

import com.yt.auth.domain.entity.AuthUserBo;
import com.yt.auth.domain.service.AuthUserDomainService;
import com.yt.subject.api.entity.AuthUserDto;
import com.yt.subject.api.entity.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user/")
@Slf4j
public class UserController {

    @Resource
    private AuthUserDomainService userDomainService;

    /**
     * 用户注册功能
     */
    @RequestMapping("/register")
    public Result<Boolean> register(@RequestBody AuthUserDto authUserDto) {
        try {
            if(log.isInfoEnabled()) {
                log.info("UserController.register.controller:{}", JSON.toJSONString(authUserDto));
            }
            Preconditions.checkNotNull(authUserDto.getUserName(),"用户名不能为空");
            Preconditions.checkNotNull(authUserDto.getPassword(),"密码不能为空");
            Preconditions.checkNotNull(authUserDto.getEmail(),"邮件不能为空");
            AuthUserBo authUserBo = UserDtoConvert.INSTANCE.dtoToBo(authUserDto);
            return Result.success(userDomainService.register(authUserBo));
        } catch (Exception e) {
            log.error("UserController.register.error:{}", e.getMessage(),e);
            return Result.error("注册用户失败");
        }
    }

    @RequestMapping("/doLogin")
    public Result<SaTokenInfo> doLogin(@RequestParam("validCode")String validCode) {
        try {
            if(log.isInfoEnabled()) {
                log.info("UserController.doLogin.controller:{}", JSON.toJSONString(validCode));
            }
            Preconditions.checkArgument(!StringUtils.isBlank(validCode),"验证码不能为空");
            SaTokenInfo saTokenInfo=userDomainService.doLogin(validCode);
            log.info("UserController.doLogin.saTokenInfo:{}", JSON.toJSONString(saTokenInfo));
            return Result.success(saTokenInfo);
        } catch (Exception e) {
            log.error("UserController.register.error:{}", e.getMessage(),e);
            return Result.error("登录失败");
        }
    }



    /**
     * 修改用户功能
     */
    @RequestMapping("/update")
    public Result<Boolean> update(@RequestBody AuthUserDto authUserDto){
        try {
            if(log.isInfoEnabled()) {
                log.info("UserController.update.controller:{}", JSON.toJSONString(authUserDto));
            }
            Preconditions.checkNotNull(authUserDto.getUserName(),"用户名不能为空");
            Preconditions.checkNotNull(authUserDto.getEmail(),"邮件不能为空");
            AuthUserBo authUserBo = UserDtoConvert.INSTANCE.dtoToBo(authUserDto);
            Boolean rs=userDomainService.update(authUserBo);
            return Result.success(rs);
        }catch (Exception e){
            log.error("UserController.update error:{}", e.getMessage(),e);
            return Result.error("更新用户失败");
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/getUserInfo")
    public Result<AuthUserDto> getUserInfo(@RequestBody AuthUserDto authUserDto){
        try {
            if(log.isInfoEnabled()) {
                log.info("UserController.getUserInfo.controller:{}", JSON.toJSONString(authUserDto));
            }
            Preconditions.checkNotNull(authUserDto.getUserName(),"用户名不能为空");
            AuthUserBo authUserBo = UserDtoConvert.INSTANCE.dtoToBo(authUserDto);
            AuthUserBo authUserbo =userDomainService.getUserInfo(authUserBo);
            AuthUserDto userDto = UserDtoConvert.INSTANCE.boToDto(authUserbo);
            return Result.success(userDto);
        }catch (Exception e){
            log.error("UserController.getUserInfo error:{}", e.getMessage(),e);
            return Result.error("获取用户信息失败");
        }
    }

    /**
     * 删除用户功能
     */
    @RequestMapping("/delete")
    public Result<Boolean> delete(@RequestBody AuthUserDto authUserDto){
        try {
            if(log.isInfoEnabled()) {
                log.info("UserController.delete.controller:{}", JSON.toJSONString(authUserDto));
            }
            AuthUserBo authUserBo = UserDtoConvert.INSTANCE.dtoToBo(authUserDto);
            return Result.success(userDomainService.delete(authUserBo));
        }catch (Exception e){
            log.error("UserController.delete.error:{}", e.getMessage(),e);
            return Result.error("删除用户失败");
        }
    }

    /**
     * 删除用户功能
     */
    @RequestMapping("/logOut")
    public Result<Boolean> logOut(@RequestParam String userName){
        try {
            if(log.isInfoEnabled()) {
                log.info("UserController.logOut.controller:{}", userName);
            }
            Preconditions.checkNotNull(userName,"用户名不能为空");
            StpUtil.logout(userName);
            return Result.success(true);
        }catch (Exception e){
            log.error("UserController.logOut.error:{}", e.getMessage(),e);
            return Result.error("用户退出失败");
        }
    }

    /**
     * 用户启用/禁用功能
     */
    @RequestMapping("/changeStatus")
    public Result<Boolean> changeStatus(@RequestBody AuthUserDto authUserDto){
        try {
            if(log.isInfoEnabled()) {
                log.info("UserController.changeStatus.controller:{}", JSON.toJSONString(authUserDto));
            }
            Preconditions.checkNotNull(authUserDto.getStatus(),"用户状态不能为空");
            AuthUserBo authUserBo = UserDtoConvert.INSTANCE.dtoToBo(authUserDto);
            Boolean rs=userDomainService.updateId(authUserBo);
            return Result.success(rs);
        }catch (Exception e){
            log.error("UserController.changeStatus error:{}", e.getMessage(),e);
            return Result.error("更新用户状态失败");
        }
    }




}
