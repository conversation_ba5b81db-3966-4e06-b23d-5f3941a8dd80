package com.yt.subject.domain.convert;

import com.yt.subject.domain.entity.SubjectAnswerBo;
import com.yt.subject.domain.entity.SubjectInfoBo;
import com.yt.subject.domain.entity.SubjectOptionBo;
import com.yt.subject.infra.basic.entity.SubjectInfo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T00:24:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class SubjectBoConvertImpl implements SubjectBoConvert {

    @Override
    public SubjectInfo boToY(SubjectInfoBo subjectInfoBo) {
        if ( subjectInfoBo == null ) {
            return null;
        }

        SubjectInfo subjectInfo = new SubjectInfo();

        subjectInfo.setId( subjectInfoBo.getId() );
        subjectInfo.setSubjectName( subjectInfoBo.getSubjectName() );
        subjectInfo.setSubjectDifficult( subjectInfoBo.getSubjectDifficult() );
        subjectInfo.setSettleName( subjectInfoBo.getSettleName() );
        subjectInfo.setSubjectType( subjectInfoBo.getSubjectType() );
        subjectInfo.setSubjectScore( subjectInfoBo.getSubjectScore() );
        subjectInfo.setSubjectParse( subjectInfoBo.getSubjectParse() );

        return subjectInfo;
    }

    @Override
    public List<SubjectInfoBo> yToBo(List<SubjectInfo> subjectInfoList) {
        if ( subjectInfoList == null ) {
            return null;
        }

        List<SubjectInfoBo> list = new ArrayList<SubjectInfoBo>( subjectInfoList.size() );
        for ( SubjectInfo subjectInfo : subjectInfoList ) {
            list.add( subjectInfoToSubjectInfoBo( subjectInfo ) );
        }

        return list;
    }

    @Override
    public SubjectInfoBo convertOptionAndInfoToBo(SubjectOptionBo optionBO, SubjectInfo subjectInfo) {
        if ( optionBO == null && subjectInfo == null ) {
            return null;
        }

        SubjectInfoBo subjectInfoBo = new SubjectInfoBo();

        if ( optionBO != null ) {
            subjectInfoBo.setSubjectAnswer( optionBO.getSubjectAnswer() );
            List<SubjectAnswerBo> list = optionBO.getOptionList();
            if ( list != null ) {
                subjectInfoBo.setOptionList( new ArrayList<SubjectAnswerBo>( list ) );
            }
        }
        if ( subjectInfo != null ) {
            subjectInfoBo.setId( subjectInfo.getId() );
            subjectInfoBo.setSubjectName( subjectInfo.getSubjectName() );
            subjectInfoBo.setSubjectDifficult( subjectInfo.getSubjectDifficult() );
            subjectInfoBo.setSettleName( subjectInfo.getSettleName() );
            subjectInfoBo.setSubjectType( subjectInfo.getSubjectType() );
            subjectInfoBo.setSubjectScore( subjectInfo.getSubjectScore() );
            subjectInfoBo.setSubjectParse( subjectInfo.getSubjectParse() );
        }

        return subjectInfoBo;
    }

    protected SubjectInfoBo subjectInfoToSubjectInfoBo(SubjectInfo subjectInfo) {
        if ( subjectInfo == null ) {
            return null;
        }

        SubjectInfoBo subjectInfoBo = new SubjectInfoBo();

        subjectInfoBo.setId( subjectInfo.getId() );
        subjectInfoBo.setSubjectName( subjectInfo.getSubjectName() );
        subjectInfoBo.setSubjectDifficult( subjectInfo.getSubjectDifficult() );
        subjectInfoBo.setSettleName( subjectInfo.getSettleName() );
        subjectInfoBo.setSubjectType( subjectInfo.getSubjectType() );
        subjectInfoBo.setSubjectScore( subjectInfo.getSubjectScore() );
        subjectInfoBo.setSubjectParse( subjectInfo.getSubjectParse() );

        return subjectInfoBo;
    }
}
