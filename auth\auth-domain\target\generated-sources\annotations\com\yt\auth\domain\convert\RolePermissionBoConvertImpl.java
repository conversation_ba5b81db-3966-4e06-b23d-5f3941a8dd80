package com.yt.auth.domain.convert;

import com.yt.auth.domain.entity.AuthRolePermissionBo;
import com.yt.auth.infra.basic.entity.AuthRolePermission;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:33:31+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class RolePermissionBoConvertImpl implements RolePermissionBoConvert {

    @Override
    public AuthRolePermission boToy(AuthRolePermissionBo authRolePermissionBo) {
        if ( authRolePermissionBo == null ) {
            return null;
        }

        AuthRolePermission authRolePermission = new AuthRolePermission();

        authRolePermission.setId( authRolePermissionBo.getId() );
        authRolePermission.setRoleId( authRolePermissionBo.getRoleId() );
        authRolePermission.setPermissionId( authRolePermissionBo.getPermissionId() );
        authRolePermission.setIsDeleted( authRolePermissionBo.getIsDeleted() );

        return authRolePermission;
    }
}
